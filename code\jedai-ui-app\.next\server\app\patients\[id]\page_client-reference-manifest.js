globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/patients/[id]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/redux-provider.tsx":{"*":{"id":"(ssr)/./app/redux-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/IdleHandler.tsx":{"*":{"id":"(ssr)/./components/IdleHandler.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/RoleGuard.tsx":{"*":{"id":"(ssr)/./components/RoleGuard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/touch-optimized-layout.tsx":{"*":{"id":"(ssr)/./components/touch-optimized-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dental-practice-dashboard.tsx":{"*":{"id":"(ssr)/./components/dental-practice-dashboard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/patients/page.tsx":{"*":{"id":"(ssr)/./app/patients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/patients/[id]/page.tsx":{"*":{"id":"(ssr)/./app/patients/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\redux-provider.tsx":{"id":"(app-pages-browser)/./app/redux-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\IdleHandler.tsx":{"id":"(app-pages-browser)/./components/IdleHandler.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\RoleGuard.tsx":{"id":"(app-pages-browser)/./components/RoleGuard.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\touch-optimized-layout.tsx":{"id":"(app-pages-browser)/./components/touch-optimized-layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\react-toastify\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\components\\dental-practice-dashboard.tsx":{"id":"(app-pages-browser)/./components/dental-practice-dashboard.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\page.tsx":{"id":"(app-pages-browser)/./app/patients/page.tsx","name":"*","chunks":["app/patients/page","static/chunks/app/patients/page.js"],"async":false},"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/patients/[id]/page.tsx","name":"*","chunks":["app/patients/[id]/page","static/chunks/app/patients/%5Bid%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\":[],"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\loading":[],"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\page":[],"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\[id]\\loading":[],"C:\\Users\\<USER>\\aug-jedAI\\jedai\\code\\jedai-ui-app\\app\\patients\\[id]\\page":[{"inlined":false,"path":"static/css/app/patients/[id]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/redux-provider.tsx":{"*":{"id":"(rsc)/./app/redux-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/IdleHandler.tsx":{"*":{"id":"(rsc)/./components/IdleHandler.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/RoleGuard.tsx":{"*":{"id":"(rsc)/./components/RoleGuard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/touch-optimized-layout.tsx":{"*":{"id":"(rsc)/./components/touch-optimized-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(rsc)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dental-practice-dashboard.tsx":{"*":{"id":"(rsc)/./components/dental-practice-dashboard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/patients/page.tsx":{"*":{"id":"(rsc)/./app/patients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/patients/[id]/page.tsx":{"*":{"id":"(rsc)/./app/patients/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}