"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decimal.js-light";
exports.ids = ["vendor-chunks/decimal.js-light"];
exports.modules = {

/***/ "(ssr)/./node_modules/decimal.js-light/decimal.mjs":
/*!***************************************************!*\
  !*** ./node_modules/decimal.js-light/decimal.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decimal: () => (/* binding */ Decimal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  decimal.js-light v2.5.1\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js-light\r\n *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n *  MIT Expat Licence\r\n */\r\n\r\n\r\n// ------------------------------------  EDITABLE DEFAULTS  ------------------------------------- //\r\n\r\n\r\n// The limit on the value of `precision`, and on the value of the first argument to\r\n// `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\nvar MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n\r\n  // The initial configuration properties of the Decimal constructor.\r\n  defaults = {\r\n\r\n    // These values must be integers within the stated ranges (inclusive).\r\n    // Most of these values can be changed during run-time using `Decimal.config`.\r\n\r\n    // The maximum number of significant digits of the result of a calculation or base conversion.\r\n    // E.g. `Decimal.config({ precision: 20 });`\r\n    precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n    // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\r\n    // `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n    //\r\n    // ROUND_UP         0 Away from zero.\r\n    // ROUND_DOWN       1 Towards zero.\r\n    // ROUND_CEIL       2 Towards +Infinity.\r\n    // ROUND_FLOOR      3 Towards -Infinity.\r\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    //\r\n    // E.g.\r\n    // `Decimal.rounding = 4;`\r\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n    rounding: 4,                           // 0 to 8\r\n\r\n    // The exponent value at and beneath which `toString` returns exponential notation.\r\n    // JavaScript numbers: -7\r\n    toExpNeg: -7,                          // 0 to -MAX_E\r\n\r\n    // The exponent value at and above which `toString` returns exponential notation.\r\n    // JavaScript numbers: 21\r\n    toExpPos:  21,                         // 0 to MAX_E\r\n\r\n    // The natural logarithm of 10.\r\n    // 115 digits\r\n    LN10: '2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286'\r\n  },\r\n\r\n\r\n// ------------------------------------ END OF EDITABLE DEFAULTS -------------------------------- //\r\n\r\n\r\n  Decimal,\r\n  external = true,\r\n\r\n  decimalError = '[DecimalError] ',\r\n  invalidArgument = decimalError + 'Invalid argument: ',\r\n  exponentOutOfRange = decimalError + 'Exponent out of range: ',\r\n\r\n  mathfloor = Math.floor,\r\n  mathpow = Math.pow,\r\n\r\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n  ONE,\r\n  BASE = 1e7,\r\n  LOG_BASE = 7,\r\n  MAX_SAFE_INTEGER = 9007199254740991,\r\n  MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE),    // 1286742750677284\r\n\r\n  // Decimal.prototype object\r\n  P = {};\r\n\r\n\r\n// Decimal prototype methods\r\n\r\n\r\n/*\r\n *  absoluteValue                       abs\r\n *  comparedTo                          cmp\r\n *  decimalPlaces                       dp\r\n *  dividedBy                           div\r\n *  dividedToIntegerBy                  idiv\r\n *  equals                              eq\r\n *  exponent\r\n *  greaterThan                         gt\r\n *  greaterThanOrEqualTo                gte\r\n *  isInteger                           isint\r\n *  isNegative                          isneg\r\n *  isPositive                          ispos\r\n *  isZero\r\n *  lessThan                            lt\r\n *  lessThanOrEqualTo                   lte\r\n *  logarithm                           log\r\n *  minus                               sub\r\n *  modulo                              mod\r\n *  naturalExponential                  exp\r\n *  naturalLogarithm                    ln\r\n *  negated                             neg\r\n *  plus                                add\r\n *  precision                           sd\r\n *  squareRoot                          sqrt\r\n *  times                               mul\r\n *  toDecimalPlaces                     todp\r\n *  toExponential\r\n *  toFixed\r\n *  toInteger                           toint\r\n *  toNumber\r\n *  toPower                             pow\r\n *  toPrecision\r\n *  toSignificantDigits                 tosd\r\n *  toString\r\n *  valueOf                             val\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\r\nP.absoluteValue = P.abs = function () {\r\n  var x = new this.constructor(this);\r\n  if (x.s) x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value\r\n *\r\n */\r\nP.comparedTo = P.cmp = function (y) {\r\n  var i, j, xdL, ydL,\r\n    x = this;\r\n\r\n  y = new x.constructor(y);\r\n\r\n  // Signs differ?\r\n  if (x.s !== y.s) return x.s || -y.s;\r\n\r\n  // Compare exponents.\r\n  if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\r\n\r\n  xdL = x.d.length;\r\n  ydL = y.d.length;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n    if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\r\nP.decimalPlaces = P.dp = function () {\r\n  var x = this,\r\n    w = x.d.length - 1,\r\n    dp = (w - x.e) * LOG_BASE;\r\n\r\n  // Subtract the number of trailing zeros of the last word.\r\n  w = x.d[w];\r\n  if (w) for (; w % 10 == 0; w /= 10) dp--;\r\n\r\n  return dp < 0 ? 0 : dp;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */\r\nP.dividedBy = P.div = function (y) {\r\n  return divide(this, new this.constructor(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, truncated to `precision` significant digits.\r\n *\r\n */\r\nP.dividedToIntegerBy = P.idiv = function (y) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\r\nP.equals = P.eq = function (y) {\r\n  return !this.cmp(y);\r\n};\r\n\r\n\r\n/*\r\n * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n *\r\n */\r\nP.exponent = function () {\r\n  return getBase10Exponent(this);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\r\nP.greaterThan = P.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\r\nP.greaterThanOrEqualTo = P.gte = function (y) {\r\n  return this.cmp(y) >= 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\r\nP.isInteger = P.isint = function () {\r\n  return this.e > this.d.length - 2;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\r\nP.isNegative = P.isneg = function () {\r\n  return this.s < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\r\nP.isPositive = P.ispos = function () {\r\n  return this.s > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is 0, otherwise return false.\r\n *\r\n */\r\nP.isZero = function () {\r\n  return this.s === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThan = P.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThanOrEqualTo = P.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n * `precision` significant digits.\r\n *\r\n * If no base is specified, return log[10](x).\r\n *\r\n * log[base](x) = ln(x) / ln(base)\r\n *\r\n * The maximum error of the result is 1 ulp (unit in the last place).\r\n *\r\n * [base] {number|string|Decimal} The base of the logarithm.\r\n *\r\n */\r\nP.logarithm = P.log = function (base) {\r\n  var r,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision,\r\n    wpr = pr + 5;\r\n\r\n  // Default base is 10.\r\n  if (base === void 0) {\r\n    base = new Ctor(10);\r\n  } else {\r\n    base = new Ctor(base);\r\n\r\n    // log[-b](x) = NaN\r\n    // log[0](x)  = NaN\r\n    // log[1](x)  = NaN\r\n    if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + 'NaN');\r\n  }\r\n\r\n  // log[b](-x) = NaN\r\n  // log[b](0) = -Infinity\r\n  if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n  // log[b](1) = 0\r\n  if (x.eq(ONE)) return new Ctor(0);\r\n\r\n  external = false;\r\n  r = divide(ln(x, wpr), ln(base, wpr), wpr);\r\n  external = true;\r\n\r\n  return round(r, pr);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var x = this;\r\n  y = new x.constructor(y);\r\n  return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */\r\nP.modulo = P.mod = function (y) {\r\n  var q,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // x % 0 = NaN\r\n  if (!y.s) throw Error(decimalError + 'NaN');\r\n\r\n  // Return x if x is 0.\r\n  if (!x.s) return round(new Ctor(x), pr);\r\n\r\n  // Prevent rounding of intermediate calculations.\r\n  external = false;\r\n  q = divide(x, y, 0, 1).times(y);\r\n  external = true;\r\n\r\n  return x.minus(q);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */\r\nP.naturalExponential = P.exp = function () {\r\n  return exp(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * truncated to `precision` significant digits.\r\n *\r\n */\r\nP.naturalLogarithm = P.ln = function () {\r\n  return ln(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\r\nP.negated = P.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s || 0;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */\r\nP.plus = P.add = function (y) {\r\n  var x = this;\r\n  y = new x.constructor(y);\r\n  return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\r\n};\r\n\r\n\r\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\r\nP.precision = P.sd = function (z) {\r\n  var e, sd, w,\r\n    x = this;\r\n\r\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n  e = getBase10Exponent(x) + 1;\r\n  w = x.d.length - 1;\r\n  sd = w * LOG_BASE + 1;\r\n  w = x.d[w];\r\n\r\n  // If non-zero...\r\n  if (w) {\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    for (; w % 10 == 0; w /= 10) sd--;\r\n\r\n    // Add the number of digits of the first word.\r\n    for (w = x.d[0]; w >= 10; w /= 10) sd++;\r\n  }\r\n\r\n  return z && e > sd ? e : sd;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */\r\nP.squareRoot = P.sqrt = function () {\r\n  var e, n, pr, r, s, t, wpr,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  // Negative or zero?\r\n  if (x.s < 1) {\r\n    if (!x.s) return new Ctor(0);\r\n\r\n    // sqrt(-x) = NaN\r\n    throw Error(decimalError + 'NaN');\r\n  }\r\n\r\n  e = getBase10Exponent(x);\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = Math.sqrt(+x);\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n  if (s == 0 || s == 1 / 0) {\r\n    n = digitsToString(x.d);\r\n    if ((n.length + e) % 2 == 0) n += '0';\r\n    s = Math.sqrt(n);\r\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  pr = Ctor.precision;\r\n  s = wpr = pr + 3;\r\n\r\n  // Newton-Raphson iteration.\r\n  for (;;) {\r\n    t = r;\r\n    r = t.plus(divide(x, t, wpr + 2)).times(0.5);\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\r\n      n = n.slice(wpr - 3, wpr + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n      if (s == wpr && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        round(t, pr + 1, 0);\r\n\r\n        if (t.times(t).eq(x)) {\r\n          r = t;\r\n          break;\r\n        }\r\n      } else if (n != '9999') {\r\n        break;\r\n      }\r\n\r\n      wpr += 4;\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return round(r, pr);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */\r\nP.times = P.mul = function (y) {\r\n  var carry, e, i, k, r, rL, t, xdL, ydL,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    xd = x.d,\r\n    yd = (y = new Ctor(y)).d;\r\n\r\n  // Return 0 if either is 0.\r\n  if (!x.s || !y.s) return new Ctor(0);\r\n\r\n  y.s *= x.s;\r\n  e = x.e + y.e;\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Ensure xd points to the longer array.\r\n  if (xdL < ydL) {\r\n    r = xd;\r\n    xd = yd;\r\n    yd = r;\r\n    rL = xdL;\r\n    xdL = ydL;\r\n    ydL = rL;\r\n  }\r\n\r\n  // Initialise the result array with zeros.\r\n  r = [];\r\n  rL = xdL + ydL;\r\n  for (i = rL; i--;) r.push(0);\r\n\r\n  // Multiply!\r\n  for (i = ydL; --i >= 0;) {\r\n    carry = 0;\r\n    for (k = xdL + i; k > i;) {\r\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n      r[k--] = t % BASE | 0;\r\n      carry = t / BASE | 0;\r\n    }\r\n\r\n    r[k] = (r[k] + carry) % BASE | 0;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; !r[--rL];) r.pop();\r\n\r\n  if (carry) ++e;\r\n  else r.shift();\r\n\r\n  y.d = r;\r\n  y.e = e;\r\n\r\n  return external ? round(y, Ctor.precision) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toDecimalPlaces = P.todp = function (dp, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n  if (dp === void 0) return x;\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  return round(x, dp + getBase10Exponent(x) + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = toString(x, true);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = round(new Ctor(x), dp + 1, rm);\r\n    str = toString(x, true, dp + 1);\r\n  }\r\n\r\n  return str;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var str, y,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) return toString(x);\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\r\n  str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\r\n\r\n  // To determine whether to add the minus sign look at the value before it was rounded,\r\n  // i.e. look at `x` rather than `y`.\r\n  return x.isneg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\r\nP.toInteger = P.toint = function () {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n *\r\n */\r\nP.toNumber = function () {\r\n  return +this;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n * truncated to `precision` significant digits.\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * The maximum error is 1 ulp (unit in last place).\r\n *\r\n * y {number|string|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\r\nP.toPower = P.pow = function (y) {\r\n  var e, k, pr, r, sign, yIsInt,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    guard = 12,\r\n    yn = +(y = new Ctor(y));\r\n\r\n  // pow(x, 0) = 1\r\n  if (!y.s) return new Ctor(ONE);\r\n\r\n  x = new Ctor(x);\r\n\r\n  // pow(0, y > 0) = 0\r\n  // pow(0, y < 0) = Infinity\r\n  if (!x.s) {\r\n    if (y.s < 1) throw Error(decimalError + 'Infinity');\r\n    return x;\r\n  }\r\n\r\n  // pow(1, y) = 1\r\n  if (x.eq(ONE)) return x;\r\n\r\n  pr = Ctor.precision;\r\n\r\n  // pow(x, 1) = x\r\n  if (y.eq(ONE)) return round(x, pr);\r\n\r\n  e = y.e;\r\n  k = y.d.length - 1;\r\n  yIsInt = e >= k;\r\n  sign = x.s;\r\n\r\n  if (!yIsInt) {\r\n\r\n    // pow(x < 0, y non-integer) = NaN\r\n    if (sign < 0) throw Error(decimalError + 'NaN');\r\n\r\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n  } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n    r = new Ctor(ONE);\r\n\r\n    // Max k of 9007199254740991 takes 53 loop iterations.\r\n    // Maximum digits array length; leaves [28, 34] guard digits.\r\n    e = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n    external = false;\r\n\r\n    for (;;) {\r\n      if (k % 2) {\r\n        r = r.times(x);\r\n        truncate(r.d, e);\r\n      }\r\n\r\n      k = mathfloor(k / 2);\r\n      if (k === 0) break;\r\n\r\n      x = x.times(x);\r\n      truncate(x.d, e);\r\n    }\r\n\r\n    external = true;\r\n\r\n    return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\r\n  }\r\n\r\n  // Result is negative if x is negative and the last digit of integer y is odd.\r\n  sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\r\n\r\n  x.s = 1;\r\n  external = false;\r\n  r = y.times(ln(x, pr + guard));\r\n  external = true;\r\n  r = exp(r);\r\n  r.s = sign;\r\n\r\n  return r;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var e, str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    e = getBase10Exponent(x);\r\n    str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = round(new Ctor(x), sd, rm);\r\n    e = getBase10Exponent(x);\r\n    str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\r\n  }\r\n\r\n  return str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toSignificantDigits = P.tosd = function (sd, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  }\r\n\r\n  return round(new Ctor(x), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\r\nP.toString = P.valueOf = P.val = P.toJSON = P[Symbol.for('nodejs.util.inspect.custom')] = function () {\r\n  var x = this,\r\n    e = getBase10Exponent(x),\r\n    Ctor = x.constructor;\r\n\r\n  return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n};\r\n\r\n\r\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n/*\r\n *  add                 P.minus, P.plus\r\n *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n *  exp                 P.exp, P.pow\r\n *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n *                      P.toString, divide, round, toString, exp, ln\r\n *  getLn10             P.log, ln\r\n *  getZeroString       digitsToString, toString\r\n *  ln                  P.log, P.ln, P.pow, exp\r\n *  parseDecimal        Decimal\r\n *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n *                      divide, getLn10, exp, ln\r\n *  subtract            P.minus, P.plus\r\n *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n *  truncate            P.pow\r\n *\r\n *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n */\r\n\r\n\r\nfunction add(x, y) {\r\n  var carry, d, e, i, k, len, xd, yd,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision;\r\n\r\n  // If either is zero...\r\n  if (!x.s || !y.s) {\r\n\r\n    // Return x if y is zero.\r\n    // Return y if y is non-zero.\r\n    if (!y.s) y = new Ctor(x);\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  k = x.e;\r\n  e = y.e;\r\n  xd = xd.slice();\r\n  i = k - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (i) {\r\n    if (i < 0) {\r\n      d = xd;\r\n      i = -i;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = k;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n    k = Math.ceil(pr / LOG_BASE);\r\n    len = k > len ? k + 1 : len + 1;\r\n\r\n    if (i > len) {\r\n      i = len;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n    d.reverse();\r\n    for (; i--;) d.push(0);\r\n    d.reverse();\r\n  }\r\n\r\n  len = xd.length;\r\n  i = yd.length;\r\n\r\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n  if (len - i < 0) {\r\n    i = len;\r\n    d = yd;\r\n    yd = xd;\r\n    xd = d;\r\n  }\r\n\r\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n  for (carry = 0; i;) {\r\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n    xd[i] %= BASE;\r\n  }\r\n\r\n  if (carry) {\r\n    xd.unshift(carry);\r\n    ++e;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n  y.d = xd;\r\n  y.e = e;\r\n\r\n  return external ? round(y, pr) : y;\r\n}\r\n\r\n\r\nfunction checkInt32(i, min, max) {\r\n  if (i !== ~~i || i < min || i > max) {\r\n    throw Error(invalidArgument + i);\r\n  }\r\n}\r\n\r\n\r\nfunction digitsToString(d) {\r\n  var i, k, ws,\r\n    indexOfLastWord = d.length - 1,\r\n    str = '',\r\n    w = d[0];\r\n\r\n  if (indexOfLastWord > 0) {\r\n    str += w;\r\n    for (i = 1; i < indexOfLastWord; i++) {\r\n      ws = d[i] + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n      str += ws;\r\n    }\r\n\r\n    w = d[i];\r\n    ws = w + '';\r\n    k = LOG_BASE - ws.length;\r\n    if (k) str += getZeroString(k);\r\n  } else if (w === 0) {\r\n    return '0';\r\n  }\r\n\r\n  // Remove trailing zeros of last w.\r\n  for (; w % 10 === 0;) w /= 10;\r\n\r\n  return str + w;\r\n}\r\n\r\n\r\nvar divide = (function () {\r\n\r\n  // Assumes non-zero x and k, and hence non-zero result.\r\n  function multiplyInteger(x, k) {\r\n    var temp,\r\n      carry = 0,\r\n      i = x.length;\r\n\r\n    for (x = x.slice(); i--;) {\r\n      temp = x[i] * k + carry;\r\n      x[i] = temp % BASE | 0;\r\n      carry = temp / BASE | 0;\r\n    }\r\n\r\n    if (carry) x.unshift(carry);\r\n\r\n    return x;\r\n  }\r\n\r\n  function compare(a, b, aL, bL) {\r\n    var i, r;\r\n\r\n    if (aL != bL) {\r\n      r = aL > bL ? 1 : -1;\r\n    } else {\r\n      for (i = r = 0; i < aL; i++) {\r\n        if (a[i] != b[i]) {\r\n          r = a[i] > b[i] ? 1 : -1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return r;\r\n  }\r\n\r\n  function subtract(a, b, aL) {\r\n    var i = 0;\r\n\r\n    // Subtract b from a.\r\n    for (; aL--;) {\r\n      a[aL] -= i;\r\n      i = a[aL] < b[aL] ? 1 : 0;\r\n      a[aL] = i * BASE + a[aL] - b[aL];\r\n    }\r\n\r\n    // Remove leading zeros.\r\n    for (; !a[0] && a.length > 1;) a.shift();\r\n  }\r\n\r\n  return function (x, y, pr, dp) {\r\n    var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz,\r\n      Ctor = x.constructor,\r\n      sign = x.s == y.s ? 1 : -1,\r\n      xd = x.d,\r\n      yd = y.d;\r\n\r\n    // Either 0?\r\n    if (!x.s) return new Ctor(x);\r\n    if (!y.s) throw Error(decimalError + 'Division by zero');\r\n\r\n    e = x.e - y.e;\r\n    yL = yd.length;\r\n    xL = xd.length;\r\n    q = new Ctor(sign);\r\n    qd = q.d = [];\r\n\r\n    // Result exponent may be one less than e.\r\n    for (i = 0; yd[i] == (xd[i] || 0); ) ++i;\r\n    if (yd[i] > (xd[i] || 0)) --e;\r\n\r\n    if (pr == null) {\r\n      sd = pr = Ctor.precision;\r\n    } else if (dp) {\r\n      sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\r\n    } else {\r\n      sd = pr;\r\n    }\r\n\r\n    if (sd < 0) return new Ctor(0);\r\n\r\n    // Convert precision in number of base 10 digits to base 1e7 digits.\r\n    sd = sd / LOG_BASE + 2 | 0;\r\n    i = 0;\r\n\r\n    // divisor < 1e7\r\n    if (yL == 1) {\r\n      k = 0;\r\n      yd = yd[0];\r\n      sd++;\r\n\r\n      // k is the carry.\r\n      for (; (i < xL || k) && sd--; i++) {\r\n        t = k * BASE + (xd[i] || 0);\r\n        qd[i] = t / yd | 0;\r\n        k = t % yd | 0;\r\n      }\r\n\r\n    // divisor >= 1e7\r\n    } else {\r\n\r\n      // Normalise xd and yd so highest order digit of yd is >= BASE/2\r\n      k = BASE / (yd[0] + 1) | 0;\r\n\r\n      if (k > 1) {\r\n        yd = multiplyInteger(yd, k);\r\n        xd = multiplyInteger(xd, k);\r\n        yL = yd.length;\r\n        xL = xd.length;\r\n      }\r\n\r\n      xi = yL;\r\n      rem = xd.slice(0, yL);\r\n      remL = rem.length;\r\n\r\n      // Add zeros to make remainder as long as divisor.\r\n      for (; remL < yL;) rem[remL++] = 0;\r\n\r\n      yz = yd.slice();\r\n      yz.unshift(0);\r\n      yd0 = yd[0];\r\n\r\n      if (yd[1] >= BASE / 2) ++yd0;\r\n\r\n      do {\r\n        k = 0;\r\n\r\n        // Compare divisor and remainder.\r\n        cmp = compare(yd, rem, yL, remL);\r\n\r\n        // If divisor < remainder.\r\n        if (cmp < 0) {\r\n\r\n          // Calculate trial digit, k.\r\n          rem0 = rem[0];\r\n          if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\r\n\r\n          // k will be how many times the divisor goes into the current remainder.\r\n          k = rem0 / yd0 | 0;\r\n\r\n          //  Algorithm:\r\n          //  1. product = divisor * trial digit (k)\r\n          //  2. if product > remainder: product -= divisor, k--\r\n          //  3. remainder -= product\r\n          //  4. if product was < remainder at 2:\r\n          //    5. compare new remainder and divisor\r\n          //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n          if (k > 1) {\r\n            if (k >= BASE) k = BASE - 1;\r\n\r\n            // product = divisor * trial digit.\r\n            prod = multiplyInteger(yd, k);\r\n            prodL = prod.length;\r\n            remL = rem.length;\r\n\r\n            // Compare product and remainder.\r\n            cmp = compare(prod, rem, prodL, remL);\r\n\r\n            // product > remainder.\r\n            if (cmp == 1) {\r\n              k--;\r\n\r\n              // Subtract divisor from product.\r\n              subtract(prod, yL < prodL ? yz : yd, prodL);\r\n            }\r\n          } else {\r\n\r\n            // cmp is -1.\r\n            // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n            // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n            if (k == 0) cmp = k = 1;\r\n            prod = yd.slice();\r\n          }\r\n\r\n          prodL = prod.length;\r\n          if (prodL < remL) prod.unshift(0);\r\n\r\n          // Subtract product from remainder.\r\n          subtract(rem, prod, remL);\r\n\r\n          // If product was < previous remainder.\r\n          if (cmp == -1) {\r\n            remL = rem.length;\r\n\r\n            // Compare divisor and new remainder.\r\n            cmp = compare(yd, rem, yL, remL);\r\n\r\n            // If divisor < new remainder, subtract divisor from remainder.\r\n            if (cmp < 1) {\r\n              k++;\r\n\r\n              // Subtract divisor from remainder.\r\n              subtract(rem, yL < remL ? yz : yd, remL);\r\n            }\r\n          }\r\n\r\n          remL = rem.length;\r\n        } else if (cmp === 0) {\r\n          k++;\r\n          rem = [0];\r\n        }    // if cmp === 1, k will be 0\r\n\r\n        // Add the next digit, k, to the result array.\r\n        qd[i++] = k;\r\n\r\n        // Update the remainder.\r\n        if (cmp && rem[0]) {\r\n          rem[remL++] = xd[xi] || 0;\r\n        } else {\r\n          rem = [xd[xi]];\r\n          remL = 1;\r\n        }\r\n\r\n      } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n    }\r\n\r\n    // Leading zero?\r\n    if (!qd[0]) qd.shift();\r\n\r\n    q.e = e;\r\n\r\n    return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\r\n  };\r\n})();\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n * significant digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n */\r\nfunction exp(x, sd) {\r\n  var denominator, guard, pow, sum, t, wpr,\r\n    i = 0,\r\n    k = 0,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision;\r\n\r\n  if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n\r\n  // exp(0) = 1\r\n  if (!x.s) return new Ctor(ONE);\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  t = new Ctor(0.03125);\r\n\r\n  while (x.abs().gte(0.1)) {\r\n    x = x.times(t);    // x = x / 2^5\r\n    k += 5;\r\n  }\r\n\r\n  // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\r\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n  wpr += guard;\r\n  denominator = pow = sum = new Ctor(ONE);\r\n  Ctor.precision = wpr;\r\n\r\n  for (;;) {\r\n    pow = round(pow.times(x), wpr);\r\n    denominator = denominator.times(++i);\r\n    t = sum.plus(divide(pow, denominator, wpr));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      while (k--) sum = round(sum.times(sum), wpr);\r\n      Ctor.precision = pr;\r\n      return sd == null ? (external = true, round(sum, pr)) : sum;\r\n    }\r\n\r\n    sum = t;\r\n  }\r\n}\r\n\r\n\r\n// Calculate the base 10 exponent from the base 1e7 exponent.\r\nfunction getBase10Exponent(x) {\r\n  var e = x.e * LOG_BASE,\r\n    w = x.d[0];\r\n\r\n  // Add the number of digits of the first word of the digits array.\r\n  for (; w >= 10; w /= 10) e++;\r\n  return e;\r\n}\r\n\r\n\r\nfunction getLn10(Ctor, sd, pr) {\r\n\r\n  if (sd > Ctor.LN10.sd()) {\r\n\r\n\r\n    // Reset global state in case the exception is caught.\r\n    external = true;\r\n    if (pr) Ctor.precision = pr;\r\n    throw Error(decimalError + 'LN10 precision limit exceeded');\r\n  }\r\n\r\n  return round(new Ctor(Ctor.LN10), sd);\r\n}\r\n\r\n\r\nfunction getZeroString(k) {\r\n  var zs = '';\r\n  for (; k--;) zs += '0';\r\n  return zs;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n * digits.\r\n *\r\n *  ln(n) is non-terminating (n != 1)\r\n *\r\n */\r\nfunction ln(y, sd) {\r\n  var c, c0, denominator, e, numerator, sum, t, wpr, x2,\r\n    n = 1,\r\n    guard = 10,\r\n    x = y,\r\n    xd = x.d,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision;\r\n\r\n  // ln(-x) = NaN\r\n  // ln(0) = -Infinity\r\n  if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n  // ln(1) = 0\r\n  if (x.eq(ONE)) return new Ctor(0);\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  if (x.eq(10)) {\r\n    if (sd == null) external = true;\r\n    return getLn10(Ctor, wpr);\r\n  }\r\n\r\n  wpr += guard;\r\n  Ctor.precision = wpr;\r\n  c = digitsToString(xd);\r\n  c0 = c.charAt(0);\r\n  e = getBase10Exponent(x);\r\n\r\n  if (Math.abs(e) < 1.5e15) {\r\n\r\n    // Argument reduction.\r\n    // The series converges faster the closer the argument is to 1, so using\r\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n    // later be divided by this number, then separate out the power of 10 using\r\n    // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n    // max n is 6 (gives 0.7 - 1.3)\r\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n      x = x.times(y);\r\n      c = digitsToString(x.d);\r\n      c0 = c.charAt(0);\r\n      n++;\r\n    }\r\n\r\n    e = getBase10Exponent(x);\r\n\r\n    if (c0 > 1) {\r\n      x = new Ctor('0.' + c);\r\n      e++;\r\n    } else {\r\n      x = new Ctor(c0 + '.' + c.slice(1));\r\n    }\r\n  } else {\r\n\r\n    // The argument reduction method above may result in overflow if the argument y is a massive\r\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n    x = ln(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n\r\n    Ctor.precision = pr;\r\n    return sd == null ? (external = true, round(x, pr)) : x;\r\n  }\r\n\r\n  // x is reduced to a value near 1.\r\n\r\n  // Taylor series.\r\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n  sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\r\n  x2 = round(x.times(x), wpr);\r\n  denominator = 3;\r\n\r\n  for (;;) {\r\n    numerator = round(numerator.times(x2), wpr);\r\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      sum = sum.times(2);\r\n\r\n      // Reverse the argument reduction.\r\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n      sum = divide(sum, new Ctor(n), wpr);\r\n\r\n      Ctor.precision = pr;\r\n      return sd == null ? (external = true, round(sum, pr)) : sum;\r\n    }\r\n\r\n    sum = t;\r\n    denominator += 2;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\r\nfunction parseDecimal(x, str) {\r\n  var e, i, len;\r\n\r\n  // Decimal point?\r\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = str.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +str.slice(i + 1);\r\n    str = str.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = str.length;\r\n  }\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; str.charCodeAt(i) === 48;) ++i;\r\n\r\n  // Determine trailing zeros.\r\n  for (len = str.length; str.charCodeAt(len - 1) === 48;) --len;\r\n  str = str.slice(i, len);\r\n\r\n  if (str) {\r\n    len -= i;\r\n    e = e - i - 1;\r\n    x.e = mathfloor(e / LOG_BASE);\r\n    x.d = [];\r\n\r\n    // Transform base\r\n\r\n    // e is the base 10 exponent.\r\n    // i is where to slice str to get the first word of the digits array.\r\n    i = (e + 1) % LOG_BASE;\r\n    if (e < 0) i += LOG_BASE;\r\n\r\n    if (i < len) {\r\n      if (i) x.d.push(+str.slice(0, i));\r\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n      str = str.slice(i);\r\n      i = LOG_BASE - str.length;\r\n    } else {\r\n      i -= len;\r\n    }\r\n\r\n    for (; i--;) str += '0';\r\n    x.d.push(+str);\r\n\r\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\r\n  } else {\r\n\r\n    // Zero.\r\n    x.s = 0;\r\n    x.e = 0;\r\n    x.d = [0];\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n */\r\n function round(x, sd, rm) {\r\n  var i, j, k, n, rd, doRound, w, xdi,\r\n    xd = x.d;\r\n\r\n  // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n  // w: the word of xd which contains the rounding digit, a base 1e7 number.\r\n  // xdi: the index of w within xd.\r\n  // n: the number of digits of w.\r\n  // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n  // they had leading zeros)\r\n  // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n  // Get the length of the first word of the digits array xd.\r\n  for (n = 1, k = xd[0]; k >= 10; k /= 10) n++;\r\n  i = sd - n;\r\n\r\n  // Is the rounding digit in the first word of xd?\r\n  if (i < 0) {\r\n    i += LOG_BASE;\r\n    j = sd;\r\n    w = xd[xdi = 0];\r\n  } else {\r\n    xdi = Math.ceil((i + 1) / LOG_BASE);\r\n    k = xd.length;\r\n    if (xdi >= k) return x;\r\n    w = k = xd[xdi];\r\n\r\n    // Get the number of digits of w.\r\n    for (n = 1; k >= 10; k /= 10) n++;\r\n\r\n    // Get the index of rd within w.\r\n    i %= LOG_BASE;\r\n\r\n    // Get the index of rd within w, adjusted for leading zeros.\r\n    // The number of leading zeros of w is given by LOG_BASE - n.\r\n    j = i - LOG_BASE + n;\r\n  }\r\n\r\n  if (rm !== void 0) {\r\n    k = mathpow(10, n - j - 1);\r\n\r\n    // Get the rounding digit at index j of w.\r\n    rd = w / k % 10 | 0;\r\n\r\n    // Are there any non-zero digits after the rounding digit?\r\n    doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\r\n\r\n    // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\r\n    // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\r\n    // 714.\r\n\r\n    doRound = rm < 4\r\n      ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n      : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 &&\r\n\r\n        // Check whether the digit to the left of the rounding digit is odd.\r\n        ((i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n          rm == (x.s < 0 ? 8 : 7));\r\n  }\r\n\r\n  if (sd < 1 || !xd[0]) {\r\n    if (doRound) {\r\n      k = getBase10Exponent(x);\r\n      xd.length = 1;\r\n\r\n      // Convert sd to decimal places.\r\n      sd = sd - k - 1;\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n      x.e = mathfloor(-sd / LOG_BASE) || 0;\r\n    } else {\r\n      xd.length = 1;\r\n\r\n      // Zero.\r\n      xd[0] = x.e = x.s = 0;\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n  // Remove excess digits.\r\n  if (i == 0) {\r\n    xd.length = xdi;\r\n    k = 1;\r\n    xdi--;\r\n  } else {\r\n    xd.length = xdi + 1;\r\n    k = mathpow(10, LOG_BASE - i);\r\n\r\n    // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n    // j > 0 means i > number of leading zeros of w.\r\n    xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\r\n  }\r\n\r\n  if (doRound) {\r\n    for (;;) {\r\n\r\n      // Is the digit to be rounded up in the first word of xd?\r\n      if (xdi == 0) {\r\n        if ((xd[0] += k) == BASE) {\r\n          xd[0] = 1;\r\n          ++x.e;\r\n        }\r\n\r\n        break;\r\n      } else {\r\n        xd[xdi] += k;\r\n        if (xd[xdi] != BASE) break;\r\n        xd[xdi--] = 0;\r\n        k = 1;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n\r\n  if (external && (x.e > MAX_E || x.e < -MAX_E)) {\r\n    throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\nfunction subtract(x, y) {\r\n  var d, e, i, j, k, len, xd, xe, xLTy, yd,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision;\r\n\r\n  // Return y negated if x is zero.\r\n  // Return x if y is zero and x is non-zero.\r\n  if (!x.s || !y.s) {\r\n    if (y.s) y.s = -y.s;\r\n    else y = new Ctor(x);\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n\r\n  // x and y are non-zero numbers with the same sign.\r\n\r\n  e = y.e;\r\n  xe = x.e;\r\n  xd = xd.slice();\r\n  k = xe - e;\r\n\r\n  // If exponents differ...\r\n  if (k) {\r\n    xLTy = k < 0;\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      k = -k;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = xe;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Numbers with massively different exponents would result in a very high number of zeros\r\n    // needing to be prepended, but this can be avoided while still ensuring correct rounding by\r\n    // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n    if (k > i) {\r\n      k = i;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents.\r\n    d.reverse();\r\n    for (i = k; i--;) d.push(0);\r\n    d.reverse();\r\n\r\n  // Base 1e7 exponents equal.\r\n  } else {\r\n\r\n    // Check digits to determine which is the bigger number.\r\n\r\n    i = xd.length;\r\n    len = yd.length;\r\n    xLTy = i < len;\r\n    if (xLTy) len = i;\r\n\r\n    for (i = 0; i < len; i++) {\r\n      if (xd[i] != yd[i]) {\r\n        xLTy = xd[i] < yd[i];\r\n        break;\r\n      }\r\n    }\r\n\r\n    k = 0;\r\n  }\r\n\r\n  if (xLTy) {\r\n    d = xd;\r\n    xd = yd;\r\n    yd = d;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  len = xd.length;\r\n\r\n  // Append zeros to xd if shorter.\r\n  // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\r\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n  // Subtract yd from xd.\r\n  for (i = yd.length; i > k;) {\r\n    if (xd[--i] < yd[i]) {\r\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n      --xd[j];\r\n      xd[i] += BASE;\r\n    }\r\n\r\n    xd[i] -= yd[i];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xd[--len] === 0;) xd.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n  // Zero?\r\n  if (!xd[0]) return new Ctor(0);\r\n\r\n  y.d = xd;\r\n  y.e = e;\r\n\r\n  //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\r\n  return external ? round(y, pr) : y;\r\n}\r\n\r\n\r\nfunction toString(x, isExp, sd) {\r\n  var k,\r\n    e = getBase10Exponent(x),\r\n    str = digitsToString(x.d),\r\n    len = str.length;\r\n\r\n  if (isExp) {\r\n    if (sd && (k = sd - len) > 0) {\r\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n    } else if (len > 1) {\r\n      str = str.charAt(0) + '.' + str.slice(1);\r\n    }\r\n\r\n    str = str + (e < 0 ? 'e' : 'e+') + e;\r\n  } else if (e < 0) {\r\n    str = '0.' + getZeroString(-e - 1) + str;\r\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n  } else if (e >= len) {\r\n    str += getZeroString(e + 1 - len);\r\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n  } else {\r\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n    if (sd && (k = sd - len) > 0) {\r\n      if (e + 1 === len) str += '.';\r\n      str += getZeroString(k);\r\n    }\r\n  }\r\n\r\n  return x.s < 0 ? '-' + str : str;\r\n}\r\n\r\n\r\n// Does not strip trailing zeros.\r\nfunction truncate(arr, len) {\r\n  if (arr.length > len) {\r\n    arr.length = len;\r\n    return true;\r\n  }\r\n}\r\n\r\n\r\n// Decimal methods\r\n\r\n\r\n/*\r\n *  clone\r\n *  config/set\r\n */\r\n\r\n\r\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\r\nfunction clone(obj) {\r\n  var i, p, ps;\r\n\r\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * value {number|string|Decimal} A numeric value.\r\n   *\r\n   */\r\n  function Decimal(value) {\r\n    var x = this;\r\n\r\n    // Decimal called without new.\r\n    if (!(x instanceof Decimal)) return new Decimal(value);\r\n\r\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n    // which points to Object.\r\n    x.constructor = Decimal;\r\n\r\n    // Duplicate.\r\n    if (value instanceof Decimal) {\r\n      x.s = value.s;\r\n      x.e = value.e;\r\n      x.d = (value = value.d) ? value.slice() : value;\r\n      return;\r\n    }\r\n\r\n    if (typeof value === 'number') {\r\n\r\n      // Reject Infinity/NaN.\r\n      if (value * 0 !== 0) {\r\n        throw Error(invalidArgument + value);\r\n      }\r\n\r\n      if (value > 0) {\r\n        x.s = 1;\r\n      } else if (value < 0) {\r\n        value = -value;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 0;\r\n        x.e = 0;\r\n        x.d = [0];\r\n        return;\r\n      }\r\n\r\n      // Fast path for small integers.\r\n      if (value === ~~value && value < 1e7) {\r\n        x.e = 0;\r\n        x.d = [value];\r\n        return;\r\n      }\r\n\r\n      return parseDecimal(x, value.toString());\r\n    } else if (typeof value !== 'string') {\r\n      throw Error(invalidArgument + value);\r\n    }\r\n\r\n    // Minus sign?\r\n    if (value.charCodeAt(0) === 45) {\r\n      value = value.slice(1);\r\n      x.s = -1;\r\n    } else {\r\n      x.s = 1;\r\n    }\r\n\r\n    if (isDecimal.test(value)) parseDecimal(x, value);\r\n    else throw Error(invalidArgument + value);\r\n  }\r\n\r\n  Decimal.prototype = P;\r\n\r\n  Decimal.ROUND_UP = 0;\r\n  Decimal.ROUND_DOWN = 1;\r\n  Decimal.ROUND_CEIL = 2;\r\n  Decimal.ROUND_FLOOR = 3;\r\n  Decimal.ROUND_HALF_UP = 4;\r\n  Decimal.ROUND_HALF_DOWN = 5;\r\n  Decimal.ROUND_HALF_EVEN = 6;\r\n  Decimal.ROUND_HALF_CEIL = 7;\r\n  Decimal.ROUND_HALF_FLOOR = 8;\r\n\r\n  Decimal.clone = clone;\r\n  Decimal.config = Decimal.set = config;\r\n\r\n  if (obj === void 0) obj = {};\r\n  if (obj) {\r\n    ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'LN10'];\r\n    for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n  }\r\n\r\n  Decimal.config(obj);\r\n\r\n  return Decimal;\r\n}\r\n\r\n\r\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\r\nfunction config(obj) {\r\n  if (!obj || typeof obj !== 'object') {\r\n    throw Error(decimalError + 'Object expected');\r\n  }\r\n  var i, p, v,\r\n    ps = [\r\n      'precision', 1, MAX_DIGITS,\r\n      'rounding', 0, 8,\r\n      'toExpNeg', -1 / 0, 0,\r\n      'toExpPos', 0, 1 / 0\r\n    ];\r\n\r\n  for (i = 0; i < ps.length; i += 3) {\r\n    if ((v = obj[p = ps[i]]) !== void 0) {\r\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  if ((v = obj[p = 'LN10']) !== void 0) {\r\n      if (v == Math.LN10) this[p] = new this(v);\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n  }\r\n\r\n  return this;\r\n}\r\n\r\n\r\n// Create and configure initial Decimal constructor.\r\nvar Decimal = clone(defaults);\r\n\r\n// Internal constant.\r\nONE = new Decimal(1);\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Decimal);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/decimal.js-light/decimal.mjs\n");

/***/ })

};
;